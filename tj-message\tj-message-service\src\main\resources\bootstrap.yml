server:
  port: 8085  #端口
  tomcat:
    uri-encoding: UTF-8   #服务编码
spring:
  profiles:
    active: dev
  application:
    name: message-service
  cloud:
    nacos:
      config:
        file-extension: yaml
        shared-configs: # 共享配置
          - data-id: shared-spring.yaml # 共享spring配置
            refresh: false
          - data-id: shared-redis.yaml # 共享redis配置
            refresh: false
          - data-id: shared-mybatis.yaml # 共享mybatis配置
            refresh: false
          - data-id: shared-logs.yaml # 共享日志配置
            refresh: false
          - data-id: shared-feign.yaml # 共享feign配置
            refresh: false
          - data-id: shared-mq.yaml # 共享mq配置
            refresh: false
tj:
  swagger:
    enable: true
    enableResponseWrap: true
    package-path: com.tianji.message.controller
    title: 天机学堂 - 消息中心接口文档
    description: 该服务用于站内信、通知管理、短信发送等功能
    contact-name: 传智教育·研究院
    contact-url: http://www.itcast.cn/
    contact-email: z<PERSON><PERSON><PERSON>@itcast.cn
    version: v1.0
  auth:
    resource:
      enable: true
      excludeLoginPaths:
        - /students/register
        - /users/detail/{isStaff}
        - /users/list
        - GET:/users/{id}
        - /users/{id}/type
        - /users/ids
  jdbc:
    database: tj_message
  sms:
    ali:
      accessId: LTAI5tHyKqhaN6k3u2jVeiX6
      accessSecret: ******************************
