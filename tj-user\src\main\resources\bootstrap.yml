server:
  port: 8082  #端口
  tomcat:
    uri-encoding: UTF-8   #服务编码
spring:
  profiles:
    active: dev
  application:
    name: user-service
  cloud:
    nacos:
      config:
        file-extension: yaml
        shared-configs: # 共享配置
          - data-id: shared-spring.yaml # 共享spring配置
            refresh: false
          - data-id: shared-redis.yaml # 共享redis配置
            refresh: false
          - data-id: shared-mybatis.yaml # 共享mybatis配置
            refresh: false
          - data-id: shared-logs.yaml # 共享日志配置
            refresh: false
          - data-id: shared-feign.yaml # 共享feign配置
            refresh: false
tj:
  swagger:
    enable: true
    enableResponseWrap: true
    package-path: com.tianji.user.controller
    title: 天机学堂 - 用户中心接口文档
    description: 该服务用户管理、用户信息管理
    contact-name: 传智教育·研究院
    contact-url: http://www.itcast.cn/
    contact-email: <EMAIL>
    version: v1.0
  auth:
    resource:
      enable: true
      excludeLoginPaths:
        - /students/register
        - /users/detail/{isStaff}
        - /users/list
        - GET:/users/{id}
        - /users/{id}/type
        - /users/ids
        - /users/me
  jdbc:
    database: tj_user