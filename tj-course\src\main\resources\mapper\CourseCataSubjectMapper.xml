<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tianji.course.mapper.CourseCataSubjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tianji.course.domain.po.CourseCataSubject">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="cata_id" property="cataId" />
        <result column="subject_id" property="subjectId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, cata_id, subject_id
    </sql>

</mapper>
