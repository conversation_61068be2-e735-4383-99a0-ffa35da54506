<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tianji.learning.mapper.LearningRecordMapper">

    <select id="countLearnedSections" resultType="com.tianji.api.dto.IdAndNumDTO">
        SELECT lesson_id AS id, COUNT(1) AS num
        FROM learning_record
        WHERE user_id = #{userId}
        AND finished = 1
        AND finish_time &gt; #{begin} AND finish_time &lt; #{end}
        GROUP BY lesson_id;
    </select>
</mapper>
