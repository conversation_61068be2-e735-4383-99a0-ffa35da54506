<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.tianji</groupId>
    <artifactId>tjxt</artifactId>
    <version>1.0.0</version>
    <modules>
        <module>tj-common</module>
        <module>tj-auth</module>
        <module>tj-api</module>
        <module>tj-gateway</module>
        <module>tj-user</module>
        <module>tj-message</module>
        <module>tj-media</module>
        <module>tj-course</module>
        <module>tj-search</module>
        <module>tj-learning</module>
        <module>tj-pay</module>
        <module>tj-trade</module>
        <module>tj-exam</module>
        <module>tj-promotion</module>
        <module>tj-data</module>
        <module>tj-remark</module>
        <module>tj-promotion</module>
    </modules>
    <packaging>pom</packaging>

    <!-- 继承 Spring Boot 父工程 -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.2</version>
    </parent>


    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <tjxt.version>1.0</tjxt.version>
        <org.projectlombok.version>1.18.20</org.projectlombok.version>
        <spring-cloud.version>2021.0.3</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.1.0</spring-cloud-alibaba.version>
        <mybatis-plus.version>3.4.3</mybatis-plus.version>
        <hutool.version>5.7.17</hutool.version>
        <swagger.version>3.0.3</swagger.version>
        <mysql.version>8.0.23</mysql.version>
        <ali.sdk.core.version>4.6.0</ali.sdk.core.version>
        <ali.sdk.kms.version>2.10.1</ali.sdk.kms.version>
        <ali.sdk.oss.version>3.10.2</ali.sdk.oss.version>
        <ali.sdk.pay.version>4.33.12.ALL</ali.sdk.pay.version>
        <tencent.cloud.version>3.1.515</tencent.cloud.version>
        <redisson.version>3.13.6</redisson.version>
        <elasticsearch.version>7.12.1</elasticsearch.version>
        <tencent.sdk.cos.version>5.6.89</tencent.sdk.cos.version>
        <tencent.sdk.vod.version>2.1.5</tencent.sdk.vod.version>
        <xxl-job-version>2.3.1</xxl-job-version>
        <seata-version>1.5.1</seata-version>
    </properties>
    <!-- 对依赖包进行管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud 依赖包管理 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- 数据库驱动包管理 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- mybatis plus 管理 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--knife4j-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!--腾讯云-->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencent.cloud.version}</version>
            </dependency>
            <!--ali sdk-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${ali.sdk.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-kms</artifactId>
                <version>${ali.sdk.kms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${ali.sdk.pay.version}</version>
            </dependency>
            <!--阿里云OSS的SDK-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${ali.sdk.oss.version}</version>
            </dependency>
            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <!--腾讯cos-->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${tencent.sdk.cos.version}</version>
            </dependency>
            <!--腾讯vod-->
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>vod_api</artifactId>
                <version>${tencent.sdk.vod.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!-- lombok 管理 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${org.projectlombok.version}</version>
        </dependency>
        <!--单元测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--开启bootstrap文件读取-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>11</source> <!-- depending on your project -->
                        <target>11</target> <!-- depending on your project -->
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>