<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tianji.learning.mapper.LearningLessonMapper">

    <select id="queryTotalPlan" resultType="java.lang.Integer">
        SELECT SUM(week_freq)
        FROM learning_lesson
        WHERE user_id = #{userId}
        AND plan_status = 1 AND status IN (0, 1)
    </select>
</mapper>
