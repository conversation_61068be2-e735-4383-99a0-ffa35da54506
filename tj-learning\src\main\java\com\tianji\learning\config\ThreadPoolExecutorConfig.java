package com.tianji.learning.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
public class ThreadPoolExecutorConfig {

    @Bean("delayTaskThreadPoolExecutor")
    public ThreadPoolExecutor delayTaskThreadPoolExecutor(){
        AtomicInteger c = new AtomicInteger(1);
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(
                17,
                32,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(r, "延迟任务线程-pool" + c.getAndIncrement()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        return poolExecutor;
    }
}
