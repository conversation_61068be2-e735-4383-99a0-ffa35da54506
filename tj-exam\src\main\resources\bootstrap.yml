server:
  port: 8089  #端口
  tomcat:
    uri-encoding: UTF-8   #服务编码
  error:
    include-message: always #返回的响应体带上message
spring:
  profiles:
    active: dev
  application:
    name: exam-service
  cloud:
    nacos:
      config:
        file-extension: yaml
        shared-configs: # 共享配置
          - dataId: shared-spring.yaml # 共享spring配置
          - dataId: shared-redis.yaml # 共享redis配置
          - dataId: shared-mybatis.yaml # 共享mybatis配置
          - dataId: shared-logs.yaml # 共享日志配置
          - dataId: shared-feign.yaml # 共享feign配置
          - dataId: shared-mq.yaml # 共享mq配置
          - dataId: shared-xxljob.yaml # 共享mq配置
          - dataId: shared-seata.yaml # 共享seata配置
tj:
  swagger:
    enable: true
    enableResponseWrap: true
    package-path: com.tianji.exam.controller
    title: 天机学堂 - 考试中心接口文档
    description: 该服务提供题目管理、考试管理、数据统计等功能
    contact-name: 传智教育·研究院
    contact-url: http://www.itcast.cn/
    contact-email: <EMAIL>
    version: v1.0
  jdbc:
    database: tj_exam
  auth:
    resource:
      enable: false