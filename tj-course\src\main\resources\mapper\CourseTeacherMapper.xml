<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tianji.course.mapper.CourseTeacherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tianji.course.domain.po.CourseTeacher">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="teacher_id" property="teacherId" />
        <result column="is_show" property="isShow" />
        <result column="c_index" property="cIndex" />
        <result column="dep_id" property="depId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creater" property="creater" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, teacher_id, is_show, c_index, dep_id, create_time, update_time, creater, updater, deleted
    </sql>

</mapper>
