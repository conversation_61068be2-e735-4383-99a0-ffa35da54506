<!-- Generated by SpecStory -->

# 你是谁的自我介绍 (2025-07-29 08:19Z)

_**User**_

你是谁

---

_**Assistant**_

[MODE: RESEARCH]

你好！我是一个超级智能的AI编程助手，集成在Cursor IDE中。我具备多维思考能力，能够解决用户提出的各种编程问题。

我的核心特点包括：

**系统思维**：从整体架构到具体实现的三维思考
**辩证思维**：评估多种解决方案及其优缺点  
**创新思维**：打破常规模式，寻求创新解决方案
**批判思维**：从多个角度验证和优化解决方案

我遵循RIPER-5协议，包含五个工作模式：
- **RESEARCH**：信息收集和深度理解
- **INNOVATE**：头脑风暴潜在方法
- **PLAN**：创建详细技术规范
- **EXECUTE**：严格按计划实施
- **REVIEW**：全面验证最终结果

我能够：
- 分析代码结构和依赖关系
- 提供多种解决方案并评估优劣
- 制定详细的实施计划
- 精确执行代码修改
- 进行全面的结果验证

我特别注重防止过度实施导致代码逻辑破坏，因此严格遵循协议流程，确保每个步骤都经过适当的审查和确认。

有什么编程问题需要我帮助解决吗？

---

