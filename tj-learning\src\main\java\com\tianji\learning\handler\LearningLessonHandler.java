package com.tianji.learning.handler;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tianji.common.domain.query.PageQuery;
import com.tianji.common.utils.CollUtils;
import com.tianji.learning.domain.po.LearningLesson;
import com.tianji.learning.enums.LessonStatus;
import com.tianji.learning.service.ILearningLessonService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class LearningLessonHandler {

    private final ILearningLessonService lessonService;

    /**
     * 检查learning_lesson表中的课程是否过期定时任务
     */
    @XxlJob("checkExpiredLessonJob")
    public void checkExpiredLesson() {
        log.debug("开始检查learning_lesson表中的过期课程");
        // 1. 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startRange = now.minusDays(1);
        // 2. 分批处理
        int pageSize = 500;
        int pageNo = 1;
        while (true) {
            // 3. 获取分页数据
            Page<LearningLesson> page = lessonService.lambdaQuery()
                    .select(LearningLesson::getId)
                    .between(LearningLesson::getExpireTime, startRange, now)
                    .in(LearningLesson::getStatus,
                            LessonStatus.NOT_BEGIN.getValue(),
                            LessonStatus.LEARNING.getValue(),
                            LessonStatus.FINISHED.getValue())
                    .page(new Page<>(pageNo, pageSize));
            List<LearningLesson> records = page.getRecords();
            if (CollUtils.isEmpty(records)) {
                return;
            }
            // 4. 修改课程状态为已过期
            // 4.1 获取过期课表id
            Set<Long> lessonIds = records.stream()
                    .map(LearningLesson::getId)
                    .collect(Collectors.toSet());
            // 4.2 修改过期课表状态
            lessonService.lambdaUpdate()
                    .set(LearningLesson::getStatus, LessonStatus.EXPIRED.getValue())
                    .in(LearningLesson::getId, lessonIds)
                    .update();
            pageNo++;
        }
    }
}
